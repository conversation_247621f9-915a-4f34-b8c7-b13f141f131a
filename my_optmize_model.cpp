﻿#include "my_optmize_model.h"
#include <QDebug>

// CellData 方法实现
CellData::CellData(const QString& str)
{
    if (str.isEmpty()) {
        type         = CellDataType::EmptyString;
        string_index = 0;
    }
    else {
        type         = CellDataType::String;
        string_index = StringPool::instance().addString(str);
    }
}

QString CellData::toString() const
{
    switch (type) {
    case CellDataType::Integer:
        return QString::number(int_value);
    case CellDataType::Double:
        return QString::number(double_value);
    case CellDataType::Boolean:
        return bool_value ? "true" : "false";
    case CellDataType::EmptyString:
        return QString("");
    case CellDataType::String:
    default:
        return StringPool::instance().getString(string_index);
    }
}

QVariant CellData::toVariant() const
{
    switch (type) {
    case CellDataType::Integer:
        return QVariant(int_value);
    case CellDataType::Double:
        return QVariant(double_value);
    case CellDataType::Boolean:
        return QVariant(bool_value);
    case CellDataType::EmptyString:
        return QVariant(QString(""));
    case CellDataType::String:
    default:
        return QVariant(StringPool::instance().getString(string_index));
    }
}

MyOptmizeModel::MyOptmizeModel(QObject* parent)
{
    use_optimized_storage_ = false;   // 默认使用原有存储方式保持兼容性
    ultra_memory_mode_     = false;   // 默认关闭超级内存模式
}

int MyOptmizeModel::rowCount(const QModelIndex& parent) const
{
    if (use_optimized_storage_) {
        return model_data_optimized_.count();
    }
    else {
        return 0;   // 修复：当未使用优化存储时返回0
    }
}

int MyOptmizeModel::columnCount(const QModelIndex& parent) const
{
    int count = model_title_list_.count();
    return count;
}

QVariant MyOptmizeModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid() || index.column() >= columnCount() || index.row() >= rowCount()) {
        return QVariant();
    }
    int row    = index.row();
    int column = index.column();

    switch (role) {
    case Qt::DisplayRole:   // 显示数据用
        if (use_optimized_storage_) {
            return model_data_optimized_[row][column].toString();
        }
        else {
            return QVariant();   // 修复：当未使用优化存储时返回空值
        }
        break;

    case Qt::EditRole:
        if (use_optimized_storage_) {
            return model_data_optimized_[row][column].toVariant();
        }
        else {
            return QVariant();   // 修复：当未使用优化存储时返回空值
        }
        break;

    case Qt::TextAlignmentRole:   // 对齐处理
        return Qt::AlignCenter;
        break;

    default:
        return QVariant();
        break;
    }
    return QVariant();
}

bool MyOptmizeModel::setData(const QModelIndex& index, const QVariant& value, int role)
{
    if (role != Qt::EditRole || !index.isValid() || index.row() >= rowCount() || index.column() >= columnCount()) {
        return false;
    }
    int row    = index.row();
    int column = index.column();

    if (use_optimized_storage_) {
        model_data_optimized_[row][column] = parse_cell_data(value.toString());
        emit dataChanged(index, index);
        return true;
    }
    else {
        // 修复：当未使用优化存储时，不允许设置数据
        return false;
    }
}

QVariant MyOptmizeModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (role == Qt::DisplayRole && orientation == Qt::Horizontal) {
        if (section < model_title_list_.count()) {
            return model_title_list_[section];
        }
    }
    return QAbstractItemModel::headerData(section, orientation, role);
}

bool MyOptmizeModel::insertRows(int row, int count, const QModelIndex& parent)
{
    // 起始行row超限时，修正到两端插入
    if (row > rowCount()) {
        row = rowCount();
    }
    if (row < 0) {
        row = 0;
    }

    // 需要将修改部分的代码使用begin和end函数包起来
    beginInsertRows(parent, row, row + count - 1);

    // 添加数据
    for (int i = 0; i < count; ++i) {
        int title_count = model_title_list_.count();

        if (use_optimized_storage_) {
            // 为优化存储创建空行
            QVector<CellData> empty_row(title_count);
            for (int j = 0; j < title_count; ++j) {
                empty_row[j] = CellData(QString(""));
            }
            model_data_optimized_.insert(model_data_optimized_.begin() + row + i, empty_row);
        }
    }

    endInsertRows();

    // 发送信号
    emit dataChanged(createIndex(row, 0), createIndex(row + count - 1, columnCount() - 1));
    return true;
}

bool MyOptmizeModel::removeRows(int row, int count, const QModelIndex& parent)
{
    if (row < 0 || row >= rowCount() || row + count > rowCount()) {
        return false;
    }

    // 需要将修改部分的代码使用begin和end函数包起来
    beginRemoveRows(parent, row, row + count - 1);

    // 删除数据
    for (int i = 0; i < count; ++i) {
        if (use_optimized_storage_) {
            model_data_optimized_.remove(row);
        }
    }
    endRemoveRows();

    return true;
}

Qt::ItemFlags MyOptmizeModel::flags(const QModelIndex& index) const { return Qt::ItemIsEditable | QAbstractTableModel::flags(index); }

void MyOptmizeModel::set_title_list(QVector<QString> titles)
{
    model_title_list_ = titles;
    emit headerDataChanged(Qt::Horizontal, 0, model_title_list_.count());
}

// 初始化模型
void MyOptmizeModel::initialize_model()
{
    // 清空所有数据
    delete_all_row();

    // 清空标题
    model_title_list_.clear();

    // 重置存储模式
    use_optimized_storage_ = false;
    ultra_memory_mode_     = false;

    // 清空字符串池
    StringPool::instance().clear();

    qDebug() << "模型已初始化";
}

void MyOptmizeModel::delete_first_row() { removeRow(0); }

void MyOptmizeModel::delete_last_row() { removeRow(rowCount() - 1); }

void MyOptmizeModel::delete_all_row()
{
    int count = rowCount();
    if (count > 0) {
        removeRows(0, count);
    }

    // 清空数据容器
    if (use_optimized_storage_) {
        model_data_optimized_.clear();
    }
}

QVector<QString> MyOptmizeModel::get_title_list() { return model_title_list_; }

QVector<QVector<QString>> MyOptmizeModel::get_model_data()
{
    if (use_optimized_storage_) {
        // 转换优化存储为QString格式
        QVector<QVector<QString>> result;
        for (const auto& row : model_data_optimized_) {
            QVector<QString> string_row;
            for (const auto& cell : row) {
                string_row.append(cell.toString());
            }
            result.append(string_row);
        }
        return result;
    }
    else {
        // 修复：当未使用优化存储时返回空数据
        return QVector<QVector<QString>>();
    }
}

// 新增：优化的行添加方法
void MyOptmizeModel::append_row(const QVector<CellData>& row_data)
{
    use_optimized_storage_ = true;   // 启用优化存储

    // 不限制行数,正常追加行数据
    int current_row = rowCount();
    insertRow(current_row);
    // 确保数据容器大小正确
    if (model_data_optimized_.size() <= current_row) {
        model_data_optimized_.resize(current_row + 1);
    }
    model_data_optimized_[current_row] = row_data;
    return;
}

// 新增：自动检测数据类型的方法
CellData MyOptmizeModel::parse_cell_data(const QString& text)
{
    QString trimmed = text.trimmed();

    // 空字符串直接返回
    if (trimmed.isEmpty()) {
        CellData cell;
        cell.type         = CellDataType::EmptyString;
        cell.string_index = 0;
        return cell;
    }

    // 检查是否为布尔值
    QString lower = trimmed.toLower();
    if (lower == "true" || lower == "false") {
        return CellData(lower == "true");
    }

    // 检查是否为整数（避免科学计数法等复杂情况）
    bool ok_int  = false;
    int  int_val = trimmed.toInt(&ok_int);
    if (ok_int && !trimmed.contains('.') && !trimmed.contains('e', Qt::CaseInsensitive)) {
        return CellData(int_val);
    }

    // 检查是否为浮点数
    bool   ok_double  = false;
    double double_val = trimmed.toDouble(&ok_double);
    if (ok_double && (trimmed.contains('.') || trimmed.contains('e', Qt::CaseInsensitive))) {
        return CellData(double_val);
    }

    // 默认为字符串
    return CellData(trimmed);
}

// 内存使用统计
size_t MyOptmizeModel::getMemoryUsage() const
{
    size_t total = 0;

    if (use_optimized_storage_) {
        // CellData 数组的内存
        total += model_data_optimized_.size() * sizeof(QVector<CellData>);
        for (const auto& row : model_data_optimized_) {
            total += row.size() * sizeof(CellData);
        }

        // 字符串池的内存
        total += StringPool::instance().getMemoryUsage();
    }


    // 标题列表的内存
    for (const auto& title : model_title_list_) {
        total += title.size() * sizeof(QChar) + sizeof(QString);
    }

    return total;
}

void MyOptmizeModel::printMemoryStats() const
{
    size_t memory = getMemoryUsage();
    qDebug() << "=== 内存使用统计 ===";
    qDebug() << "存储模式:" << (use_optimized_storage_ ? "优化存储" : "传统存储");
    qDebug() << "行数:" << rowCount() << "列数:" << columnCount();
    qDebug() << "总内存使用:" << memory << "字节 (" << memory / 1024 / 1024 << "MB)";

    if (use_optimized_storage_) {
        qDebug() << "字符串池内存:" << StringPool::instance().getMemoryUsage() / 1024 / 1024 << "MB";
        qDebug() << "平均每个单元格:" << (rowCount() * columnCount() > 0 ? memory / (rowCount() * columnCount()) : 0) << "字节";
    }
}

// 启用超级内存模式
void MyOptmizeModel::enableUltraMemoryMode(bool enable)
{
    ultra_memory_mode_ = enable;
    if (enable) {
        compactStringPool();
    }
}

// 压缩字符串池
void MyOptmizeModel::compactStringPool()
{
    if (!use_optimized_storage_)
        return;

    qDebug() << "开始压缩字符串池...";
    size_t before = StringPool::instance().getMemoryUsage();

    // 这里可以实现字符串池的压缩逻辑
    // 例如：移除未使用的字符串、合并相似字符串等

    size_t after = StringPool::instance().getMemoryUsage();
    qDebug() << "字符串池压缩完成，节省内存:" << (before - after) / 1024 << "KB";
}
